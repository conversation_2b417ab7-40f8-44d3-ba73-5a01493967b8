'use client';

import { Box } from '@mui/material';
import * as React from 'react';

export interface IPhoneFrameProps {
  children: React.ReactNode;
  width?: number;
  height?: number;
}

export function IPhoneFrame({ children, width = 320, height = 640 }: IPhoneFrameProps): React.JSX.Element {
  const aspectRatio = 19.5 / 9; // iPhone 16 aspect ratio
  const calculatedHeight = width * aspectRatio;
  const frameHeight = height || calculatedHeight;

  return (
    <Box
      sx={{
        position: 'relative',
        width: width,
        height: frameHeight,
        mx: 'auto',
        // iPhone 16 frame styling
        bgcolor: '#1a1a1a', // Dark frame color
        borderRadius: '60px',
        padding: '8px',
        boxShadow: `
          0 0 0 2px #333,
          0 0 0 4px #1a1a1a,
          0 20px 40px rgba(0, 0, 0, 0.4),
          inset 0 0 0 1px rgba(255, 255, 255, 0.1)
        `,
        '&::before': {
          content: '""',
          position: 'absolute',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '120px',
          height: '30px',
          bgcolor: '#1a1a1a',
          borderRadius: '0 0 20px 20px',
          zIndex: 10,
          // Dynamic Island styling
          boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.3)',
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          top: '28px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '6px',
          height: '6px',
          bgcolor: '#333',
          borderRadius: '50%',
          zIndex: 11,
          // Camera dot
        },
      }}
    >
      {/* Screen area */}
      <Box
        sx={{
          width: '100%',
          height: '100%',
          bgcolor: '#000',
          borderRadius: '52px',
          overflow: 'hidden',
          position: 'relative',
          // Screen content area
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Status bar area */}
        <Box
          sx={{
            height: '50px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px: 3,
            pt: 1,
            color: 'white',
            fontSize: '14px',
            fontWeight: 600,
            zIndex: 5,
          }}
        >
          <Box>9:41</Box>
          <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
            {/* Signal bars */}
            <Box sx={{ display: 'flex', gap: '2px', alignItems: 'end' }}>
              <Box sx={{ width: '3px', height: '4px', bgcolor: 'white', borderRadius: '1px' }} />
              <Box sx={{ width: '3px', height: '6px', bgcolor: 'white', borderRadius: '1px' }} />
              <Box sx={{ width: '3px', height: '8px', bgcolor: 'white', borderRadius: '1px' }} />
              <Box sx={{ width: '3px', height: '10px', bgcolor: 'white', borderRadius: '1px' }} />
            </Box>
            {/* WiFi icon */}
            <Box sx={{ ml: 1, fontSize: '12px' }}>📶</Box>
            {/* Battery */}
            <Box
              sx={{
                ml: 1,
                width: '24px',
                height: '12px',
                border: '1px solid white',
                borderRadius: '2px',
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  right: '-3px',
                  top: '3px',
                  width: '2px',
                  height: '6px',
                  bgcolor: 'white',
                  borderRadius: '0 1px 1px 0',
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  left: '1px',
                  top: '1px',
                  width: '18px',
                  height: '8px',
                  bgcolor: 'white',
                  borderRadius: '1px',
                },
              }}
            />
          </Box>
        </Box>

        {/* Content area */}
        <Box
          sx={{
            flex: 1,
            position: 'relative',
            overflow: 'hidden',
            // Gradient background for iOS feel
            background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
}
